#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 Enhanced Top 20 Binaries Analysis with ECS Validation and Sphinx Reporting

This script runs the enhanced top 20 binaries analysis that includes:
- Real Windows 10 VM execution
- ECS data validation and checking
- Comprehensive report generation
- Sphinx documentation generation

🚀 **ENHANCED FEATURES:**
- ECS data validation for each binary
- Real-time Elasticsearch connectivity checking
- Comprehensive report generation using existing templates
- Automatic Sphinx documentation generation
- Data quality assessment and recommendations

📊 **WORKFLOW:**
1. Download and upload binaries to TurdParty
2. Create Windows 10 VMs and execute binaries
3. Validate ECS data collection across multiple indexes
4. Generate individual reports using existing report generator
5. Create ECS comprehensive reports JSON
6. Process through Sphinx template generator
7. Build HTML documentation

🎯 **OUTPUT:**
- Individual binary analysis reports
- ECS comprehensive reports JSON file
- Sphinx HTML documentation
- Data quality assessment and recommendations

Usage:
    python scripts/run-enhanced-top20-analysis.py
"""

import sys
import os
from pathlib import Path
import requests
import json
import time

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_ecs_events(binary_name, injection_id=None, vm_id=None):
    """
    Step 7: Check ECS event count and debug if zero events found.

    Args:
        binary_name: Name of the binary being analyzed
        injection_id: Optional injection ID for correlation
        vm_id: Optional VM ID for correlation

    Returns:
        int: Number of ECS events found
    """
    import requests
    import json
    import time

    print(f"\n🔍 Step 7: Checking ECS events for {binary_name}...")

    elasticsearch_url = "http://elasticsearch.turdparty.localhost"

    # Search queries for different event types
    search_queries = [
        {
            "name": "install_events",
            "index": "turdparty-install-*",
            "query": {
                "query": {
                    "bool": {
                        "must": [
                            {"match": {"binary_name": binary_name}},
                            {"range": {"@timestamp": {"gte": "now-1h"}}}
                        ]
                    }
                }
            }
        },
        {
            "name": "runtime_events",
            "index": "turdparty-runtime-*",
            "query": {
                "query": {
                    "bool": {
                        "must": [
                            {"match": {"binary_name": binary_name}},
                            {"range": {"@timestamp": {"gte": "now-1h"}}}
                        ]
                    }
                }
            }
        }
    ]

    # Add injection/VM specific queries if available
    if injection_id:
        for query_config in search_queries:
            query_config["query"]["query"]["bool"]["must"].append(
                {"match": {"injection_id": injection_id}}
            )

    if vm_id:
        for query_config in search_queries:
            query_config["query"]["query"]["bool"]["must"].append(
                {"match": {"vm_id": vm_id}}
            )

    total_events = 0
    event_breakdown = {}

    for query_config in search_queries:
        try:
            response = requests.post(
                f"{elasticsearch_url}/{query_config['index']}/_search",
                headers={"Content-Type": "application/json"},
                json=query_config["query"],
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                event_count = data.get("hits", {}).get("total", {}).get("value", 0)
                event_breakdown[query_config["name"]] = event_count
                total_events += event_count
                print(f"    📊 {query_config['name']}: {event_count} events")
            else:
                print(f"    ❌ Failed to query {query_config['name']}: {response.status_code}")
                event_breakdown[query_config["name"]] = 0

        except Exception as e:
            print(f"    ❌ Error querying {query_config['name']}: {e}")
            event_breakdown[query_config["name"]] = 0

    print(f"    📈 Total ECS events for {binary_name}: {total_events}")

    # Debug if no events found
    if total_events == 0:
        print(f"\n🚨 DEBUGGING: Zero ECS events found for {binary_name}")
        print("=" * 60)

        # Check Elasticsearch connectivity
        try:
            health_response = requests.get(f"{elasticsearch_url}/_cluster/health", timeout=10)
            if health_response.status_code == 200:
                health_data = health_response.json()
                print(f"✅ Elasticsearch cluster health: {health_data.get('status', 'unknown')}")
            else:
                print(f"❌ Elasticsearch health check failed: {health_response.status_code}")
        except Exception as e:
            print(f"❌ Cannot connect to Elasticsearch: {e}")

        # Check available indices
        try:
            indices_response = requests.get(f"{elasticsearch_url}/_cat/indices/turdparty-*?format=json", timeout=10)
            if indices_response.status_code == 200:
                indices = indices_response.json()
                print(f"📋 Available TurdParty indices:")
                for index in indices:
                    print(f"    - {index.get('index', 'unknown')}: {index.get('docs.count', 0)} docs")
            else:
                print(f"❌ Failed to list indices: {indices_response.status_code}")
        except Exception as e:
            print(f"❌ Cannot list indices: {e}")

        # Check recent events in any TurdParty index
        try:
            recent_query = {
                "query": {
                    "range": {
                        "@timestamp": {
                            "gte": "now-1h"
                        }
                    }
                },
                "size": 5,
                "sort": [{"@timestamp": {"order": "desc"}}]
            }

            recent_response = requests.post(
                f"{elasticsearch_url}/turdparty-*/_search",
                headers={"Content-Type": "application/json"},
                json=recent_query,
                timeout=30
            )

            if recent_response.status_code == 200:
                recent_data = recent_response.json()
                recent_count = recent_data.get("hits", {}).get("total", {}).get("value", 0)
                print(f"📊 Recent events in all TurdParty indices (last hour): {recent_count}")

                if recent_count > 0:
                    print("🔍 Sample recent events:")
                    for hit in recent_data.get("hits", {}).get("hits", [])[:3]:
                        source = hit.get("_source", {})
                        timestamp = source.get("@timestamp", "unknown")
                        event_type = source.get("event", {}).get("type", "unknown")
                        print(f"    - {timestamp}: {event_type}")
            else:
                print(f"❌ Failed to query recent events: {recent_response.status_code}")

        except Exception as e:
            print(f"❌ Cannot query recent events: {e}")

        print("\n🛑 STOPPING FOR DEBUGGING - No ECS events detected")
        print("💡 Possible issues:")
        print("   - VM monitoring not properly configured")
        print("   - ECS logging pipeline not working")
        print("   - Binary execution failed silently")
        print("   - Elasticsearch indexing issues")
        print("   - Timing issues (events not yet indexed)")

        # Wait a bit and try one more time
        print("\n⏳ Waiting 30 seconds and checking again...")
        time.sleep(30)

        # Retry the check
        retry_total = 0
        for query_config in search_queries:
            try:
                response = requests.post(
                    f"{elasticsearch_url}/{query_config['index']}/_search",
                    headers={"Content-Type": "application/json"},
                    json=query_config["query"],
                    timeout=30
                )

                if response.status_code == 200:
                    data = response.json()
                    event_count = data.get("hits", {}).get("total", {}).get("value", 0)
                    retry_total += event_count
                    if event_count > 0:
                        print(f"    ✅ Retry found {event_count} {query_config['name']} events")

            except Exception as e:
                print(f"    ❌ Retry error for {query_config['name']}: {e}")

        if retry_total == 0:
            print(f"\n❌ Still no events after retry - stopping analysis")
            return 0
        else:
            print(f"\n✅ Retry successful - found {retry_total} events total")
            return retry_total

    return total_events


def main():
    """Run the enhanced top 20 binaries analysis."""
    print("🚀 💩🎉TurdParty🎉💩 Enhanced Top 20 Binaries Analysis")
    print("=" * 80)
    print("🎯 **REAL VM ANALYSIS + ECS VALIDATION + SPHINX REPORTING**")
    print("=" * 80)

    try:
        # Import and run the enhanced test
        import importlib.util
        spec = importlib.util.spec_from_file_location("test_top_10_binaries", project_root / "scripts" / "test-top-10-binaries.py")
        test_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(test_module)
        Top20BinariesTester = test_module.Top20BinariesTester

        print("\n📋 Initializing enhanced analysis suite...")
        tester = Top20BinariesTester()

        # Monkey patch the tester to include ECS checking after each binary
        original_analyze_method = tester.analyze_real_binary_on_vm

        def enhanced_analyze_with_ecs_check(binary_name, binary_info):
            """Enhanced analysis that includes ECS event checking."""
            print(f"\n🔥 Starting analysis for {binary_name}...")

            # Run the original analysis
            result = original_analyze_method(binary_name, binary_info)

            # Extract relevant IDs for ECS checking
            injection_id = result.get("injection_id")
            vm_id = result.get("vm_id")

            # Step 7: Check ECS events
            event_count = check_ecs_events(binary_name, injection_id, vm_id)

            # Add ECS event count to result
            result["ecs_event_count"] = event_count
            result["ecs_validation"] = "passed" if event_count > 0 else "failed"

            # Stop if no events found
            if event_count == 0:
                print(f"\n🛑 STOPPING ANALYSIS - No ECS events for {binary_name}")
                raise Exception(f"No ECS events detected for {binary_name} - debugging required")

            return result

        # Replace the method
        tester.analyze_real_binary_on_vm = enhanced_analyze_with_ecs_check

        print("🔥 Starting comprehensive analysis pipeline with ECS validation...")
        results = tester.run_complete_test()

        # Print final summary
        print(f"\n{'='*80}")
        print("🎉 ENHANCED ANALYSIS COMPLETE!")
        print(f"{'='*80}")

        successful = results.get("successful_analyses", 0)
        total = results.get("total_analyses", 0)

        print(f"📊 **FINAL RESULTS:**")
        print(f"   Total Binaries: {total}")
        print(f"   Successful Analyses: {successful}")
        print(f"   Success Rate: {results.get('success_rate', 0):.1f}%")
        print(f"   Total Time: {results.get('total_time', 0)/60:.1f} minutes")

        print(f"\n🌐 **ACCESS POINTS:**")
        print(f"   📊 TurdParty API: http://api.turdparty.localhost")
        print(f"   🔍 Kibana: http://kibana.turdparty.localhost/app/discover")
        print(f"   📋 Elasticsearch: http://elasticsearch.turdparty.localhost")
        print(f"   📚 Sphinx Reports: docs/reports-ecs/_build/html/index.html")

        print(f"\n📋 **GENERATED OUTPUTS:**")
        print(f"   📄 Analysis Report: top20_windows_vm_analysis_*.txt")
        print(f"   📊 ECS Reports: ecs_comprehensive_reports_*.json")
        print(f"   📚 Sphinx Documentation: docs/reports-ecs/")

        if successful > 0:
            print(f"\n✅ Analysis completed successfully!")
            return 0
        else:
            print(f"\n⚠️ No successful analyses completed")
            return 1

    except KeyboardInterrupt:
        print("\n⚠️ Analysis interrupted by user")
        return 130
    except Exception as e:
        print(f"\n❌ Enhanced analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
