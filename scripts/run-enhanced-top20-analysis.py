#!/usr/bin/env python3
"""
💩🎉TurdParty🎉💩 Enhanced Top 20 Binaries Analysis with ECS Validation and Sphinx Reporting

This script runs the enhanced top 20 binaries analysis that includes:
- Real Windows 10 VM execution
- ECS data validation and checking
- Comprehensive report generation
- Sphinx documentation generation

🚀 **ENHANCED FEATURES:**
- ECS data validation for each binary
- Real-time Elasticsearch connectivity checking
- Comprehensive report generation using existing templates
- Automatic Sphinx documentation generation
- Data quality assessment and recommendations

📊 **WORKFLOW:**
1. Download and upload binaries to TurdParty
2. Create Windows 10 VMs and execute binaries
3. Validate ECS data collection across multiple indexes
4. Generate individual reports using existing report generator
5. Create ECS comprehensive reports JSON
6. Process through Sphinx template generator
7. Build HTML documentation

🎯 **OUTPUT:**
- Individual binary analysis reports
- ECS comprehensive reports JSON file
- Sphinx HTML documentation
- Data quality assessment and recommendations

Usage:
    python scripts/run-enhanced-top20-analysis.py
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Run the enhanced top 20 binaries analysis."""
    print("🚀 💩🎉TurdParty🎉💩 Enhanced Top 20 Binaries Analysis")
    print("=" * 80)
    print("🎯 **REAL VM ANALYSIS + ECS VALIDATION + SPHINX REPORTING**")
    print("=" * 80)
    
    try:
        # Import and run the enhanced test
        import importlib.util
        spec = importlib.util.spec_from_file_location("test_top_10_binaries", project_root / "scripts" / "test-top-10-binaries.py")
        test_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(test_module)
        Top20BinariesTester = test_module.Top20BinariesTester
        
        print("\n📋 Initializing enhanced analysis suite...")
        tester = Top20BinariesTester()
        
        print("🔥 Starting comprehensive analysis pipeline...")
        results = tester.run_complete_test()
        
        # Print final summary
        print(f"\n{'='*80}")
        print("🎉 ENHANCED ANALYSIS COMPLETE!")
        print(f"{'='*80}")
        
        successful = results.get("successful_analyses", 0)
        total = results.get("total_analyses", 0)
        
        print(f"📊 **FINAL RESULTS:**")
        print(f"   Total Binaries: {total}")
        print(f"   Successful Analyses: {successful}")
        print(f"   Success Rate: {results.get('success_rate', 0):.1f}%")
        print(f"   Total Time: {results.get('total_time', 0)/60:.1f} minutes")
        
        print(f"\n🌐 **ACCESS POINTS:**")
        print(f"   📊 TurdParty API: http://api.turdparty.localhost")
        print(f"   🔍 Kibana: http://kibana.turdparty.localhost/app/discover")
        print(f"   📋 Elasticsearch: http://elasticsearch.turdparty.localhost")
        print(f"   📚 Sphinx Reports: docs/reports-ecs/_build/html/index.html")
        
        print(f"\n📋 **GENERATED OUTPUTS:**")
        print(f"   📄 Analysis Report: top20_windows_vm_analysis_*.txt")
        print(f"   📊 ECS Reports: ecs_comprehensive_reports_*.json")
        print(f"   📚 Sphinx Documentation: docs/reports-ecs/")
        
        if successful > 0:
            print(f"\n✅ Analysis completed successfully!")
            return 0
        else:
            print(f"\n⚠️ No successful analyses completed")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ Analysis interrupted by user")
        return 130
    except Exception as e:
        print(f"\n❌ Enhanced analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
